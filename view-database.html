<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Viewer - Spika Academy</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .table-section { margin: 20px 0; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .btn { padding: 10px 15px; margin: 5px; background: #007bff; color: white; border: none; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .error { color: red; }
        .success { color: green; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Database Viewer - Spika Academy</h1>
        
        <div class="controls">
            <button class="btn" onclick="loadProfiles()">Load Users (Profiles)</button>
            <button class="btn" onclick="loadCourses()">Load Courses</button>
            <button class="btn" onclick="loadClasses()">Load Classes</button>
            <button class="btn" onclick="loadEnrollments()">Load Enrollments</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        // Khởi tạo Supabase client
        const SUPABASE_URL = "https://xotapvuospvnlhuqyyjp.supabase.co";
        const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhvdGFwdnVvc3B2bmxodXF5eWpwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc5OTI0MjAsImV4cCI6MjA2MzU2ODQyMH0.kKf-fSAik4y9sSU61nRXidNnVkv9KbXafMBP4zb0NTY";
        
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_KEY);

        function showResults(title, data, error) {
            const resultsDiv = document.getElementById('results');
            
            if (error) {
                resultsDiv.innerHTML = `
                    <div class="table-section">
                        <h2>${title}</h2>
                        <p class="error">Error: ${error.message}</p>
                    </div>
                `;
                return;
            }

            if (!data || data.length === 0) {
                resultsDiv.innerHTML = `
                    <div class="table-section">
                        <h2>${title}</h2>
                        <p>No data found</p>
                    </div>
                `;
                return;
            }

            const keys = Object.keys(data[0]);
            const tableHTML = `
                <div class="table-section">
                    <h2>${title} (${data.length} records)</h2>
                    <table>
                        <thead>
                            <tr>${keys.map(key => `<th>${key}</th>`).join('')}</tr>
                        </thead>
                        <tbody>
                            ${data.map(row => 
                                `<tr>${keys.map(key => `<td>${JSON.stringify(row[key])}</td>`).join('')}</tr>`
                            ).join('')}
                        </tbody>
                    </table>
                </div>
            `;
            
            resultsDiv.innerHTML = tableHTML;
        }

        async function loadProfiles() {
            try {
                const { data, error } = await supabase
                    .from('profiles')
                    .select('*')
                    .order('created_at', { ascending: false });
                
                showResults('Users (Profiles)', data, error);
            } catch (err) {
                showResults('Users (Profiles)', null, err);
            }
        }

        async function loadCourses() {
            try {
                const { data, error } = await supabase
                    .from('courses')
                    .select('*')
                    .order('created_at', { ascending: false });
                
                showResults('Courses', data, error);
            } catch (err) {
                showResults('Courses', null, err);
            }
        }

        async function loadClasses() {
            try {
                const { data, error } = await supabase
                    .from('classes')
                    .select(`
                        *,
                        courses(name),
                        profiles(fullname)
                    `)
                    .order('created_at', { ascending: false });
                
                showResults('Classes', data, error);
            } catch (err) {
                showResults('Classes', null, err);
            }
        }

        async function loadEnrollments() {
            try {
                const { data, error } = await supabase
                    .from('enrollments')
                    .select(`
                        *,
                        profiles(fullname, email),
                        classes(name)
                    `)
                    .order('enrolled_at', { ascending: false });
                
                showResults('Enrollments', data, error);
            } catch (err) {
                showResults('Enrollments', null, err);
            }
        }

        // Load profiles by default
        window.onload = () => {
            loadProfiles();
        };
    </script>
</body>
</html>
